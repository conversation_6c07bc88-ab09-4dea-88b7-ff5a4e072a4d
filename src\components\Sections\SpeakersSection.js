import React from 'react';
import Container from '../Layout/Container';
import Card from '../UI/Card';
import useResponsive from '../../hooks/useResponsive';

const SpeakersSection = () => {
  const { isMobile } = useResponsive();

  // Placeholder speaker data - replace with actual data
  const speakers = [
    {
      id: 1,
      name: 'Speaker Name 1',
      title: 'CEO & Founder',
      company: 'Company Name',
      image: '/api/placeholder/300/300',
      bio: 'Brief speaker biography goes here...'
    },
    {
      id: 2,
      name: 'Speaker Name 2',
      title: 'Senior Executive',
      company: 'Company Name',
      image: '/api/placeholder/300/300',
      bio: 'Brief speaker biography goes here...'
    },
    {
      id: 3,
      name: 'Speaker Name 3',
      title: 'Industry Expert',
      company: 'Company Name',
      image: '/api/placeholder/300/300',
      bio: 'Brief speaker biography goes here...'
    },
  ];

  return (
    <section id="speakers" className="min-h-screen bg-gray-900 py-16 lg:py-24">
      <Container>
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className={`${isMobile ? 'text-3xl' : 'text-4xl lg:text-5xl'} font-bold mb-4 text-white`}>
            Speakers & Guests
          </h2>
          <p className={`${isMobile ? 'text-lg' : 'text-xl'} opacity-75 text-orange-300 mb-4`}>
            讲员阵容
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-amber-500 mx-auto rounded-full"></div>
        </div>

        {/* Speakers Grid */}
        <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'} gap-6 lg:gap-8`}>
          {speakers.map((speaker) => (
            <Card key={speaker.id} variant="glass" className="text-center hover:scale-105 transition-transform duration-300">
              {/* Speaker Image Placeholder */}
              <div className="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-orange-400/20 to-amber-600/20 rounded-full flex items-center justify-center border-2 border-orange-400/30">
                <div className="text-4xl text-orange-300">👤</div>
              </div>
              
              {/* Speaker Info */}
              <h3 className="text-xl font-bold text-white mb-2">{speaker.name}</h3>
              <p className="text-orange-300 font-medium mb-1">{speaker.title}</p>
              <p className="text-gray-400 text-sm mb-4">{speaker.company}</p>
              <p className="text-gray-300 text-sm leading-relaxed">{speaker.bio}</p>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12 lg:mt-16">
          <p className="text-gray-300 mb-6">
            More speakers to be announced soon!
          </p>
          <div className="inline-flex items-center space-x-2 text-orange-300">
            <span>Stay tuned for updates</span>
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SpeakersSection;
