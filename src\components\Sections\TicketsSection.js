import React from 'react';
import Container from '../Layout/Container';
import Card from '../UI/Card';
import Button from '../UI/Button';
import useResponsive from '../../hooks/useResponsive';

const TicketsSection = () => {
  const { isMobile } = useResponsive();

  const ticketTypes = [
    {
      id: 1,
      name: 'Early Bird',
      price: 'RM 299',
      originalPrice: 'RM 399',
      description: 'Limited time offer',
      features: [
        'Full 2-day conference access',
        'Welcome kit & materials',
        'Networking sessions',
        'Refreshments included',
      ],
      popular: true,
      available: true,
    },
    {
      id: 2,
      name: 'Standard',
      price: 'RM 399',
      originalPrice: null,
      description: 'Regular pricing',
      features: [
        'Full 2-day conference access',
        'Welcome kit & materials',
        'Networking sessions',
        'Refreshments included',
      ],
      popular: false,
      available: true,
    },
    {
      id: 3,
      name: 'VIP',
      price: 'RM 599',
      originalPrice: null,
      description: 'Premium experience',
      features: [
        'Full 2-day conference access',
        'Premium welcome kit',
        'VIP networking sessions',
        'Premium meals included',
        'Meet & greet with speakers',
        'Priority seating',
      ],
      popular: false,
      available: true,
    },
  ];

  return (
    <section id="tickets" className="min-h-screen bg-gray-800 py-16 lg:py-24">
      <Container>
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className={`${isMobile ? 'text-3xl' : 'text-4xl lg:text-5xl'} font-bold mb-4 text-white`}>
            Tickets
          </h2>
          <p className={`${isMobile ? 'text-lg' : 'text-xl'} opacity-75 text-orange-300 mb-4`}>
            票价
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-amber-500 mx-auto rounded-full"></div>
        </div>

        {/* Tickets Grid */}
        <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'} gap-6 lg:gap-8 max-w-6xl mx-auto`}>
          {ticketTypes.map((ticket) => (
            <Card 
              key={ticket.id} 
              variant="glass" 
              className={`relative text-center ${ticket.popular ? 'ring-2 ring-orange-500 scale-105' : ''} hover:scale-105 transition-all duration-300`}
            >
              {/* Popular Badge */}
              {ticket.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-orange-500 to-amber-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </div>
                </div>
              )}

              {/* Ticket Header */}
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">{ticket.name}</h3>
                <p className="text-gray-400 text-sm">{ticket.description}</p>
              </div>

              {/* Price */}
              <div className="mb-6">
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-4xl font-bold text-orange-300">{ticket.price}</span>
                  {ticket.originalPrice && (
                    <span className="text-lg text-gray-500 line-through">{ticket.originalPrice}</span>
                  )}
                </div>
              </div>

              {/* Features */}
              <div className="mb-8">
                <ul className="space-y-3 text-left">
                  {ticket.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <div className="w-5 h-5 rounded-full bg-orange-500/20 flex items-center justify-center mt-0.5 flex-shrink-0">
                        <div className="w-2 h-2 rounded-full bg-orange-400"></div>
                      </div>
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* CTA Button */}
              <Button 
                variant={ticket.popular ? 'primary' : 'outline'}
                size="lg"
                className="w-full"
                disabled={!ticket.available}
              >
                {ticket.available ? 'Get Tickets' : 'Sold Out'}
              </Button>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12 lg:mt-16">
          <Card variant="glass" className="max-w-2xl mx-auto">
            <h3 className="text-xl font-bold text-white mb-4">Important Information</h3>
            <div className="text-gray-300 text-sm space-y-2">
              <p>• All prices are in Malaysian Ringgit (RM)</p>
              <p>• Early bird pricing valid until limited quantities last</p>
              <p>• Tickets are non-refundable but transferable</p>
              <p>• Group discounts available for 5+ tickets</p>
            </div>
          </Card>
        </div>
      </Container>
    </section>
  );
};

export default TicketsSection;
