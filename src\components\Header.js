import React from 'react';

const BlessingAsiaLanding = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-black text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-orange-900/20 via-transparent to-orange-900/20"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-amber-500/10 rounded-full blur-3xl"></div>
      
      {/* Navigation */}
      <nav className="relative z-10 flex justify-between items-center px-8 py-6 fixed top-0 left-0 right-0 bg-black backdrop-blur-sm border-b border-gray-700/50">
        <div className="bg-gray-700 px-6 py-3 rounded">
          <span className="text-white font-semibold">TB Logo</span>
        </div>
        
        <div className="flex space-x-8 text-sm">
          <div 
            className="text-center cursor-pointer group relative"
            onClick={() => scrollToSection('speakers')}
          >
            <div className="px-4 py-3 rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-[#333] hover:to-[#666]">
                <div className="text-md transition-colors">Speakers & Guests</div>
                <div className="text-md opacity-75 group-hover:opacity-100 transition-opacity">讲员阵容</div>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-400 transition-all duration-300 group-hover:w-full"></span>
            </div>
        </div>
          <div 
            className="text-center cursor-pointer group relative"
            onClick={() => scrollToSection('tickets')}
          >
             <div className="px-4 py-3 rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-[#333] hover:to-[#666]">
                <div className="text-md transition-colors">Tickets</div>
                <div className="text-md opacity-75 group-hover:opacity-100 transition-opacity">票价</div>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-400 transition-all duration-300 group-hover:w-full"></span>
            </div>
          </div>
          <div 
            className="text-center cursor-pointer group relative"
            onClick={() => scrollToSection('schedules')}
          >
            <div className="px-4 py-3 rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-[#333] hover:to-[#666]">
                <div className="group-hover:text-white transition-colors">Schedules</div>
                <div className="text-xs opacity-75 group-hover:opacity-100 transition-opacity">日程表</div>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-400 transition-all duration-300 group-hover:w-full"></span>
            </div>
          </div>
          <div 
            className="text-center cursor-pointer group relative"
            onClick={() => scrollToSection('masterclass')}
          >
            <div className="px-4 py-3 rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-[#333] hover:to-[#666]e">
                <div className="group-hover:text-white transition-colors">Masterclass</div>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-400 transition-all duration-300 group-hover:w-full"></span>
            </div>
          </div>
          <div 
            className="text-center cursor-pointer group relative"
            onClick={() => scrollToSection('faq')}
          >
            <div className="px-4 py-3 rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-[#333] hover:to-[#666]">
                <div className="group-hover:text-white transition-colors">FAQ</div>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-400 transition-all duration-300 group-hover:w-full"></span>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-[80vh] px-8">
        {/* Top Section */}
        <div className="flex justify-between items-start w-full max-w-7xl mb-12">
          {/* Left Text */}
          <div className="text-left max-w-md">
            <h2 className="text-xl mb-2 text-orange-300">打造新格局·创造新价值</h2>
            <p className="text-lg opacity-90">Redefining the Future</p>
            <p className="text-lg opacity-90">Reimagining Value</p>
          </div>
          
          {/* Right Logo */}
          <div className="text-right">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 px-6 py-4 rounded-lg">
              <div className="text-2xl font-bold tracking-wider">TB</div>
              <div className="text-xs opacity-75 mt-1">THE BLESSING</div>
            </div>
          </div>
        </div>

        {/* Center Title */}
        <div className="text-center mb-8">
          <h1 className="text-6xl font-bold tracking-wider mb-4 text-orange-100">
            造 局 者
          </h1>
          <p className="text-xl tracking-widest text-orange-200 opacity-90">
            SHAPER OF TOMORROW
          </p>
        </div>

        {/* Trophy/Chalice Placeholder */}
        <div className="relative mb-12">
          <div className="w-64 h-80 bg-gradient-to-b from-amber-400/20 to-orange-600/30 rounded-t-full mx-auto relative">
            {/* Trophy Base */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-16 bg-gradient-to-b from-amber-600/40 to-amber-800/60 rounded-b-lg"></div>
            
            {/* Trophy Bowl */}
            <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-48 h-32 bg-gradient-to-b from-amber-300/30 to-amber-600/40 rounded-t-full border border-amber-400/30"></div>
            
            {/* Trophy Handles */}
            <div className="absolute top-16 left-4 w-8 h-16 bg-amber-400/20 rounded-l-full border-l border-amber-400/30"></div>
            <div className="absolute top-16 right-4 w-8 h-16 bg-amber-400/20 rounded-r-full border-r border-amber-400/30"></div>
            
            {/* Fire/Flame Effects */}
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-16 h-20 bg-gradient-to-t from-orange-500/40 via-amber-400/60 to-yellow-300/80 rounded-full blur-sm animate-pulse"></div>
          </div>
        </div>

        {/* Main Event Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <h1 className="text-7xl font-bold italic">
              <span className="text-orange-200">The </span>
              <span className="text-amber-300">Blessing</span>
            </h1>
            <div className="ml-4 text-right">
              <div className="text-sm font-semibold text-orange-300">ASIA</div>
            </div>
          </div>
          
          <div className="bg-orange-200/90 text-gray-900 px-8 py-3 rounded-full inline-block">
            <span className="font-semibold text-lg">亚洲职场蒙福特会</span>
          </div>
        </div>

        {/* Date and Venue */}
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-2 text-orange-200">22-23 AUG 2025</h2>
          <p className="text-lg text-orange-300">Hyatt Regency Kota Kinabalu</p>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-1/3 left-8 w-2 h-16 bg-gradient-to-b from-transparent via-orange-400/50 to-transparent rotate-45"></div>
      <div className="absolute top-1/2 right-8 w-2 h-20 bg-gradient-to-b from-transparent via-amber-400/50 to-transparent -rotate-45"></div>
      <div className="absolute bottom-1/4 left-1/4 w-1 h-12 bg-gradient-to-b from-transparent via-orange-300/50 to-transparent rotate-12"></div>
      
      {/* Floating Particles Effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/3 w-1 h-1 bg-orange-400 rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-amber-300 rounded-full opacity-40 animate-pulse delay-700"></div>
        <div className="absolute bottom-1/3 left-1/5 w-1 h-1 bg-orange-300 rounded-full opacity-50 animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 right-1/3 w-1 h-1 bg-amber-400 rounded-full opacity-60 animate-pulse delay-300"></div>
      </div>

      {/* Content Sections for SPA navigation */}
      <div id="speakers" className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold mb-4">Speakers & Guests</h2>
          <p className="text-xl opacity-75">讲员阵容</p>
          <p className="mt-8 text-lg">Content for speakers section goes here...</p>
        </div>
      </div>

      <div id="tickets" className="min-h-screen bg-gray-800 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold mb-4">Tickets</h2>
          <p className="text-xl opacity-75">票价</p>
          <p className="mt-8 text-lg">Content for tickets section goes here...</p>
        </div>
      </div>

      <div id="schedules" className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold mb-4">Schedules</h2>
          <p className="text-xl opacity-75">日程表</p>
          <p className="mt-8 text-lg">Content for schedules section goes here...</p>
        </div>
      </div>

      <div id="masterclass" className="min-h-screen bg-gray-800 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold mb-4">Masterclass</h2>
          <p className="mt-8 text-lg">Content for masterclass section goes here...</p>
        </div>
      </div>

      <div id="faq" className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold mb-4">FAQ</h2>
          <p className="mt-8 text-lg">Content for FAQ section goes here...</p>
        </div>
      </div>
    </div>
  );
};

export default BlessingAsiaLanding;