import React, { useState } from 'react';
import Container from '../Layout/Container';
import Card from '../UI/Card';
import useResponsive from '../../hooks/useResponsive';

const SchedulesSection = () => {
  const { isMobile } = useResponsive();
  const [activeDay, setActiveDay] = useState(1);

  const scheduleData = {
    1: [
      {
        time: '08:00 - 09:00',
        title: 'Registration & Welcome Coffee',
        speaker: '',
        type: 'registration'
      },
      {
        time: '09:00 - 09:30',
        title: 'Opening Ceremony',
        speaker: 'Event Host',
        type: 'ceremony'
      },
      {
        time: '09:30 - 10:30',
        title: 'Keynote: Shaping Tomorrow\'s Workplace',
        speaker: 'Keynote Speaker',
        type: 'keynote'
      },
      {
        time: '10:30 - 11:00',
        title: 'Networking Break',
        speaker: '',
        type: 'break'
      },
      {
        time: '11:00 - 12:00',
        title: 'Panel Discussion: Future of Work',
        speaker: 'Industry Leaders',
        type: 'panel'
      },
      {
        time: '12:00 - 13:30',
        title: 'Lunch & Networking',
        speaker: '',
        type: 'break'
      },
      {
        time: '13:30 - 14:30',
        title: 'Workshop: Leadership in Digital Age',
        speaker: 'Workshop Leader',
        type: 'workshop'
      },
      {
        time: '14:30 - 15:00',
        title: 'Coffee Break',
        speaker: '',
        type: 'break'
      },
      {
        time: '15:00 - 16:00',
        title: 'Fireside Chat: Innovation & Growth',
        speaker: 'Special Guest',
        type: 'talk'
      },
      {
        time: '16:00 - 17:00',
        title: 'Day 1 Closing & Networking',
        speaker: '',
        type: 'networking'
      }
    ],
    2: [
      {
        time: '08:30 - 09:00',
        title: 'Day 2 Registration & Coffee',
        speaker: '',
        type: 'registration'
      },
      {
        time: '09:00 - 10:00',
        title: 'Morning Keynote: Building Resilient Teams',
        speaker: 'Keynote Speaker',
        type: 'keynote'
      },
      {
        time: '10:00 - 10:30',
        title: 'Networking Break',
        speaker: '',
        type: 'break'
      },
      {
        time: '10:30 - 11:30',
        title: 'Masterclass Sessions (Parallel)',
        speaker: 'Various Experts',
        type: 'masterclass'
      },
      {
        time: '11:30 - 12:30',
        title: 'Panel: Workplace Transformation',
        speaker: 'Industry Experts',
        type: 'panel'
      },
      {
        time: '12:30 - 14:00',
        title: 'Lunch & Awards Ceremony',
        speaker: '',
        type: 'ceremony'
      },
      {
        time: '14:00 - 15:00',
        title: 'Closing Keynote: The Blessed Workplace',
        speaker: 'Closing Speaker',
        type: 'keynote'
      },
      {
        time: '15:00 - 15:30',
        title: 'Closing Ceremony & Next Steps',
        speaker: 'Event Host',
        type: 'ceremony'
      },
      {
        time: '15:30 - 16:30',
        title: 'Final Networking & Farewell',
        speaker: '',
        type: 'networking'
      }
    ]
  };

  const getTypeColor = (type) => {
    const colors = {
      registration: 'bg-blue-500/20 text-blue-300',
      ceremony: 'bg-purple-500/20 text-purple-300',
      keynote: 'bg-orange-500/20 text-orange-300',
      break: 'bg-gray-500/20 text-gray-300',
      panel: 'bg-green-500/20 text-green-300',
      workshop: 'bg-amber-500/20 text-amber-300',
      talk: 'bg-pink-500/20 text-pink-300',
      networking: 'bg-indigo-500/20 text-indigo-300',
      masterclass: 'bg-red-500/20 text-red-300'
    };
    return colors[type] || 'bg-gray-500/20 text-gray-300';
  };

  return (
    <section id="schedules" className="min-h-screen bg-gray-900 py-16 lg:py-24">
      <Container>
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className={`${isMobile ? 'text-3xl' : 'text-4xl lg:text-5xl'} font-bold mb-4 text-white`}>
            Schedules
          </h2>
          <p className={`${isMobile ? 'text-lg' : 'text-xl'} opacity-75 text-orange-300 mb-4`}>
            日程表
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-amber-500 mx-auto rounded-full"></div>
        </div>

        {/* Day Selector */}
        <div className="flex justify-center mb-8 lg:mb-12">
          <div className="bg-gray-800/50 rounded-lg p-1 flex">
            {[1, 2].map((day) => (
              <button
                key={day}
                onClick={() => setActiveDay(day)}
                className={`px-6 py-3 rounded-md transition-all duration-300 ${
                  activeDay === day
                    ? 'bg-gradient-to-r from-orange-500 to-amber-500 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Day {day}
                <div className="text-xs mt-1">
                  {day === 1 ? '22 AUG' : '23 AUG'}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Schedule Timeline */}
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {scheduleData[activeDay].map((item, index) => (
              <Card key={index} variant="glass" className="hover:bg-white/5 transition-colors duration-300">
                <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'items-center space-x-6'}`}>
                  {/* Time */}
                  <div className={`${isMobile ? 'text-center' : 'text-right'} flex-shrink-0`}>
                    <div className="text-orange-300 font-bold text-lg">{item.time}</div>
                  </div>

                  {/* Content */}
                  <div className="flex-grow">
                    <div className="flex items-start space-x-4">
                      {/* Type Indicator */}
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)} flex-shrink-0`}>
                        {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                      </div>
                      
                      {/* Details */}
                      <div className="flex-grow">
                        <h3 className="text-white font-semibold text-lg mb-1">{item.title}</h3>
                        {item.speaker && (
                          <p className="text-gray-400 text-sm">{item.speaker}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12 lg:mt-16">
          <Card variant="glass" className="max-w-2xl mx-auto">
            <h3 className="text-xl font-bold text-white mb-4">Schedule Notes</h3>
            <div className="text-gray-300 text-sm space-y-2">
              <p>• All times are in Malaysia Time (GMT+8)</p>
              <p>• Schedule subject to change</p>
              <p>• Detailed speaker information will be updated soon</p>
              <p>• Networking sessions include refreshments</p>
            </div>
          </Card>
        </div>
      </Container>
    </section>
  );
};

export default SchedulesSection;
