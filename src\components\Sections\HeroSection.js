import React from 'react';
import Container from '../Layout/Container';
import useResponsive from '../../hooks/useResponsive';

const HeroSection = () => {
  const { isMobile, isTablet } = useResponsive();

  return (
    <section className="relative z-10 min-h-screen flex flex-col items-center justify-center pt-20 pb-8">
      <Container size="default" padding="default">
        {/* Top Section */}
        <div className={`flex ${isMobile ? 'flex-col space-y-6' : 'justify-between items-start'} w-full mb-8 lg:mb-12`}>
          {/* Left Text */}
          <div className={`${isMobile ? 'text-center' : 'text-left'} max-w-md`}>
            <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} mb-2 text-orange-300`}>
              打造新格局·创造新价值
            </h2>
            <p className={`${isMobile ? 'text-base' : 'text-lg'} opacity-90`}>
              Redefining the Future
            </p>
            <p className={`${isMobile ? 'text-base' : 'text-lg'} opacity-90`}>
              Reimagining Value
            </p>
          </div>
          
          {/* Right Logo */}
          <div className={`${isMobile ? 'flex justify-center' : 'text-right'}`}>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 px-4 sm:px-6 py-3 sm:py-4 rounded-lg">
              <div className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold tracking-wider`}>
                TB
              </div>
              <div className="text-xs opacity-75 mt-1">THE BLESSING</div>
            </div>
          </div>
        </div>

        {/* Center Title */}
        <div className="text-center mb-6 lg:mb-8">
          <h1 className={`${isMobile ? 'text-4xl' : isTablet ? 'text-5xl' : 'text-6xl'} font-bold tracking-wider mb-4 text-orange-100`}>
            造 局 者
          </h1>
          <p className={`${isMobile ? 'text-lg' : 'text-xl'} tracking-widest text-orange-200 opacity-90`}>
            SHAPER OF TOMORROW
          </p>
        </div>

        {/* Trophy/Chalice */}
        <div className="relative mb-8 lg:mb-12 flex justify-center">
          <div className={`${isMobile ? 'w-48 h-60' : 'w-64 h-80'} bg-gradient-to-b from-amber-400/20 to-orange-600/30 rounded-t-full relative`}>
            {/* Trophy Base */}
            <div className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 ${isMobile ? 'w-24 h-12' : 'w-32 h-16'} bg-gradient-to-b from-amber-600/40 to-amber-800/60 rounded-b-lg`}></div>
            
            {/* Trophy Bowl */}
            <div className={`absolute ${isMobile ? 'top-6' : 'top-8'} left-1/2 transform -translate-x-1/2 ${isMobile ? 'w-36 h-24' : 'w-48 h-32'} bg-gradient-to-b from-amber-300/30 to-amber-600/40 rounded-t-full border border-amber-400/30`}></div>
            
            {/* Trophy Handles */}
            <div className={`absolute ${isMobile ? 'top-12' : 'top-16'} ${isMobile ? 'left-3' : 'left-4'} ${isMobile ? 'w-6 h-12' : 'w-8 h-16'} bg-amber-400/20 rounded-l-full border-l border-amber-400/30`}></div>
            <div className={`absolute ${isMobile ? 'top-12' : 'top-16'} ${isMobile ? 'right-3' : 'right-4'} ${isMobile ? 'w-6 h-12' : 'w-8 h-16'} bg-amber-400/20 rounded-r-full border-r border-amber-400/30`}></div>
            
            {/* Fire/Flame Effects */}
            <div className={`absolute -top-3 left-1/2 transform -translate-x-1/2 ${isMobile ? 'w-12 h-16' : 'w-16 h-20'} bg-gradient-to-t from-orange-500/40 via-amber-400/60 to-yellow-300/80 rounded-full blur-sm animate-pulse`}></div>
          </div>
        </div>

        {/* Main Event Title */}
        <div className="text-center mb-6 lg:mb-8">
          <div className={`flex ${isMobile ? 'flex-col' : 'items-center justify-center'} mb-4`}>
            <h1 className={`${isMobile ? 'text-5xl' : isTablet ? 'text-6xl' : 'text-7xl'} font-bold italic`}>
              <span className="text-orange-200">The </span>
              <span className="text-amber-300">Blessing</span>
            </h1>
            <div className={`${isMobile ? 'mt-2' : 'ml-4'} text-center`}>
              <div className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-orange-300`}>
                ASIA
              </div>
            </div>
          </div>
          
          <div className="bg-orange-200/90 text-gray-900 px-6 sm:px-8 py-2 sm:py-3 rounded-full inline-block">
            <span className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
              亚洲职场蒙福特会
            </span>
          </div>
        </div>

        {/* Date and Venue */}
        <div className="text-center">
          <h2 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold mb-2 text-orange-200`}>
            22-23 AUG 2025
          </h2>
          <p className={`${isMobile ? 'text-base' : 'text-lg'} text-orange-300`}>
            Hyatt Regency Kota Kinabalu
          </p>
        </div>
      </Container>
    </section>
  );
};

export default HeroSection;
